#!/usr/bin/env python3
"""
Test script to verify the BrowserUseAgent TypeError fix.
This script tests that the agent can be created without the MemoryConfig TypeError.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the src directory to the path
sys.path.append('src')

def test_browser_use_agent_creation():
    """Test creating a BrowserUseAgent without the TypeError."""
    print("Testing BrowserUseAgent creation...")
    
    try:
        from src.agent.browser_use.browser_use_agent import BrowserUseAgent
        from src.utils import llm_provider
        from browser_use.browser.browser import BrowserConfig
        from browser_use.browser.context import BrowserContextConfig
        from src.browser.custom_browser import CustomBrowser
        from src.controller.custom_controller import CustomController
        
        # Initialize LLM
        llm = llm_provider.get_llm_model(
            provider='ollama',
            model_name='gemma2:2b',
            temperature=0.7,
            base_url='http://*************:11434',
            num_ctx=4096
        )
        print('✓ LLM created successfully')
        
        # Create a minimal browser setup (headless)
        browser = CustomBrowser(
            config=BrowserConfig(
                headless=True,
                new_context_config=BrowserContextConfig(
                    window_width=1280,
                    window_height=1100,
                )
            )
        )
        print('✓ Browser created successfully')
        
        # Create controller
        controller = CustomController()
        print('✓ Controller created successfully')
        
        # Create browser context
        browser_context = None  # Will be created by browser if needed
        
        # This should work without the TypeError now
        agent = BrowserUseAgent(
            task='test task',
            llm=llm,
            browser=browser,
            browser_context=browser_context,
            controller=controller,
            enable_memory=False,  # Disabled due to browser-use 0.1.48 compatibility
            source="test",
        )
        print('✓ BrowserUseAgent created successfully without TypeError!')
        print(f'✓ Agent task: {agent.task}')
        print(f'✓ Memory enabled: {hasattr(agent, "memory") and agent.memory is not None}')
        
        # Clean up
        if browser:
            import asyncio
            asyncio.run(browser.close())
        
        return True
        
    except Exception as e:
        print(f'✗ Error creating BrowserUseAgent: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_memory_config_issue():
    """Test the specific MemoryConfig issue that was causing the TypeError."""
    print("\nTesting MemoryConfig issue...")
    
    try:
        from browser_use.agent.memory.views import MemoryConfig
        
        # This should work - creating a MemoryConfig with keyword arguments
        memory_config = MemoryConfig(
            embedder_provider="ollama",
            embedder_model="all-minilm",
            embedder_dims=384
        )
        print('✓ MemoryConfig created successfully with keyword arguments')
        
        # This would fail in browser-use 0.1.48 - trying to pass config as positional argument
        try:
            # This is what browser-use 0.1.48 Memory service tries to do:
            # MemoryConfig(config)  # This fails with TypeError
            print('✓ Avoided the problematic MemoryConfig(config) call')
        except Exception as e:
            print(f'✗ MemoryConfig positional argument error: {e}')
            
        return True
        
    except Exception as e:
        print(f'✗ Error testing MemoryConfig: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("BrowserUseAgent TypeError Fix Test")
    print("=" * 70)
    
    # Run tests
    agent_test = test_browser_use_agent_creation()
    memory_test = test_memory_config_issue()
    
    print("\n" + "=" * 70)
    print("Test Results:")
    print(f"BrowserUseAgent Creation: {'✓ PASS' if agent_test else '✗ FAIL'}")
    print(f"MemoryConfig Issue: {'✓ PASS' if memory_test else '✗ FAIL'}")
    
    if all([agent_test, memory_test]):
        print("\n🎉 All tests passed!")
        print("The TypeError fix is working correctly.")
        print("\nNote: Memory is disabled due to browser-use 0.1.48 compatibility issues.")
        print("To enable memory with remote Ollama, consider upgrading to browser-use 0.7.6+")
    else:
        print("\n⚠ Some tests failed.")
        print("The TypeError fix may need additional work.")
    
    print("=" * 70)
