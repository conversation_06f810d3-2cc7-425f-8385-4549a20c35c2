#!/usr/bin/env python3
"""
Test script to verify that the memory component can actually connect to the remote Ollama server.
This script creates a mem0 Memory instance with the correct configuration.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the src directory to the path
sys.path.append('src')

def test_mem0_with_remote_ollama():
    """Test creating a mem0 Memory instance with remote Ollama configuration."""
    print("Testing mem0 Memory with remote Ollama...")
    
    try:
        from mem0 import Memory
        
        # Get the Ollama base URL from environment
        ollama_base_url = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        print(f"Using Ollama base URL: {ollama_base_url}")
        
        # Create the configuration for mem0 with remote Ollama
        config = {
            "embedder": {
                "provider": "ollama",
                "config": {
                    "model": "all-minilm",
                    "embedding_dims": 384,
                    "ollama_base_url": ollama_base_url
                }
            },
            "llm": {
                "provider": "langchain",
                "config": {
                    "model": None
                }
            },
            "vector_store": {
                "provider": "faiss",
                "config": {
                    "embedding_model_dims": 384,
                    "path": "/tmp/mem0_test_384_faiss"
                }
            }
        }
        
        print("Creating mem0 Memory instance...")
        print(f"Config: {config}")
        
        # Create the Memory instance
        memory = Memory.from_config(config)
        
        print("✓ Memory instance created successfully!")
        print("✓ This means the Ollama connection configuration is working!")
        
        # Clean up
        del memory
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating Memory instance: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_browser_use_memory_config():
    """Test the browser-use MemoryConfig approach."""
    print("\nTesting browser-use MemoryConfig...")
    
    try:
        from browser_use.agent.memory.views import MemoryConfig
        
        # Get the Ollama base URL from environment
        ollama_base_url = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        
        # Create memory config
        memory_config = MemoryConfig(
            embedder_provider="ollama",
            embedder_model="all-minilm",
            embedder_dims=384
        )
        
        # Modify the config to include the Ollama base URL
        config_dict = memory_config.full_config_dict
        config_dict['embedder']['config']['ollama_base_url'] = ollama_base_url
        
        print("✓ MemoryConfig created and modified successfully!")
        print(f"Final config: {config_dict}")
        
        # Test creating Memory from this config
        from mem0 import Memory
        memory = Memory.from_config(config_dict)
        
        print("✓ Memory instance created from MemoryConfig!")
        
        # Clean up
        del memory
        
        return True
        
    except Exception as e:
        print(f"✗ Error with MemoryConfig approach: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("Memory Connection Test with Remote Ollama")
    print("=" * 70)
    
    # Run tests
    mem0_test = test_mem0_with_remote_ollama()
    browser_use_test = test_browser_use_memory_config()
    
    print("\n" + "=" * 70)
    print("Test Results:")
    print(f"Direct mem0 Memory: {'✓ PASS' if mem0_test else '✗ FAIL'}")
    print(f"browser-use MemoryConfig: {'✓ PASS' if browser_use_test else '✗ FAIL'}")
    
    if all([mem0_test, browser_use_test]):
        print("\n🎉 All memory tests passed!")
        print("The BrowserUseAgent should now be able to connect to your remote Ollama server for memory/embeddings.")
    else:
        print("\n⚠ Some memory tests failed.")
        print("There may be additional configuration needed.")
    
    print("=" * 70)
