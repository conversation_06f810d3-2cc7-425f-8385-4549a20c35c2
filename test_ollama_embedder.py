#!/usr/bin/env python3
"""
Test script to verify that the Ollama embedder can connect to the remote server.
This tests the core embedding functionality without the full Memory setup.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_ollama_embedder_direct():
    """Test the Ollama embedder directly with remote configuration."""
    print("Testing Ollama embedder with remote server...")
    
    try:
        from mem0.embeddings.ollama import OllamaEmbedding
        from mem0.embeddings.base import BaseEmbedderConfig
        
        # Get the Ollama base URL from environment
        ollama_base_url = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        print(f"Using Ollama base URL: {ollama_base_url}")
        
        # Create embedder config with remote Ollama URL
        config = BaseEmbedderConfig(
            model="all-minilm",
            embedding_dims=384,
            ollama_base_url=ollama_base_url
        )
        
        print("Creating Ollama embedder...")
        embedder = OllamaEmbedding(config)
        
        print("✓ Ollama embedder created successfully!")
        print(f"✓ Connected to: {ollama_base_url}")

        # Test if we can list models (this is what was failing before)
        print("Testing model listing...")
        try:
            models = embedder.client.list()["models"]
            model_names = [model.name if hasattr(model, 'name') else model.get('name', 'unknown') for model in models]
            print(f"✓ Successfully listed models: {model_names}")
        except Exception as e:
            print(f"✗ Failed to list models: {e}")
            return False
        
        # Test if the embedding model exists or can be pulled
        print("Testing embedding model availability...")
        try:
            # This will either use the existing model or pull it
            test_text = "This is a test sentence for embedding."
            print(f"Testing embedding with text: '{test_text}'")
            
            # Note: This might take a while if the model needs to be pulled
            embedding = embedder.embed_query(test_text)
            print(f"✓ Successfully created embedding! Dimensions: {len(embedding)}")
            
        except Exception as e:
            print(f"⚠ Embedding test failed (model might need to be pulled): {e}")
            # This is not necessarily a failure - the model might just need to be pulled
            print("This is expected if the 'all-minilm' model is not yet available on the remote server.")
            print("The model will be automatically pulled when first used.")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing Ollama embedder: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_client_connection():
    """Test basic Ollama client connection."""
    print("\nTesting basic Ollama client connection...")
    
    try:
        from ollama import Client
        
        ollama_base_url = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        print(f"Creating Ollama client for: {ollama_base_url}")
        
        client = Client(host=ollama_base_url)
        
        # Test listing models
        models = client.list()["models"]
        model_names = [model.name if hasattr(model, 'name') else model.get('name', 'unknown') for model in models]
        print(f"✓ Successfully connected and listed models: {model_names}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error with Ollama client: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("Ollama Embedder Connection Test")
    print("=" * 70)
    
    # Run tests
    client_test = test_ollama_client_connection()
    embedder_test = test_ollama_embedder_direct()
    
    print("\n" + "=" * 70)
    print("Test Results:")
    print(f"Ollama Client Connection: {'✓ PASS' if client_test else '✗ FAIL'}")
    print(f"Ollama Embedder: {'✓ PASS' if embedder_test else '✗ FAIL'}")
    
    if all([client_test, embedder_test]):
        print("\n🎉 All embedder tests passed!")
        print("The Ollama embedder can connect to your remote server.")
        print("The BrowserUseAgent memory fix should work!")
    else:
        print("\n⚠ Some embedder tests failed.")
        print("Please check the connection and model availability.")
    
    print("=" * 70)
