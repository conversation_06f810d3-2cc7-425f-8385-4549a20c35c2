#!/usr/bin/env python3
"""
Test script to verify the Ollama memory configuration fix.
This script tests the memory configuration without actually creating a full BrowserUseAgent.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the src directory to the path
sys.path.append('src')

def test_ollama_memory_config():
    """Test the Ollama memory configuration."""
    print("Testing Ollama memory configuration...")
    
    try:
        from browser_use.agent.memory.views import MemoryConfig
        from mem0.embeddings.base import BaseEmbedderConfig
        
        # Get the Ollama base URL from environment
        ollama_base_url = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        print(f"Using Ollama base URL: {ollama_base_url}")
        
        # Create memory config with Ollama embedder
        memory_config = MemoryConfig(
            embedder_provider="ollama",
            embedder_model="all-minilm",
            embedder_dims=384
        )
        
        # Modify the config to include the Ollama base URL
        config_dict = memory_config.full_config_dict
        config_dict['embedder']['config']['ollama_base_url'] = ollama_base_url
        
        print("Memory configuration created successfully!")
        print("Config details:")
        print(f"  Embedder provider: {config_dict['embedder']['provider']}")
        print(f"  Embedder model: {config_dict['embedder']['config']['model']}")
        print(f"  Embedder dimensions: {config_dict['embedder']['config']['embedding_dims']}")
        print(f"  Ollama base URL: {config_dict['embedder']['config']['ollama_base_url']}")
        
        return True
        
    except Exception as e:
        print(f"Error testing memory configuration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ollama_connection():
    """Test connection to the remote Ollama server."""
    print("\nTesting Ollama server connection...")
    
    try:
        import requests
        ollama_base_url = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        
        # Test basic connection
        response = requests.get(f"{ollama_base_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✓ Successfully connected to Ollama server at {ollama_base_url}")
            print(f"Available models: {[model['name'] for model in models]}")
            return True
        else:
            print(f"✗ Failed to connect to Ollama server. Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Error connecting to Ollama server: {e}")
        return False

def test_embedding_model_availability():
    """Test if the embedding model is available or can be pulled."""
    print("\nTesting embedding model availability...")
    
    try:
        import requests
        ollama_base_url = os.getenv("OLLAMA_ENDPOINT", "http://localhost:11434")
        
        # Check available models
        response = requests.get(f"{ollama_base_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            # Check if all-minilm is available
            if 'all-minilm' in model_names:
                print("✓ all-minilm embedding model is available")
                return True
            else:
                print("⚠ all-minilm embedding model is not available")
                print("The model will be automatically pulled when first used by mem0")
                return True
        else:
            print(f"✗ Could not check model availability. Status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Error checking model availability: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Ollama Memory Configuration Test")
    print("=" * 60)
    
    # Run tests
    config_test = test_ollama_memory_config()
    connection_test = test_ollama_connection()
    model_test = test_embedding_model_availability()
    
    print("\n" + "=" * 60)
    print("Test Results:")
    print(f"Memory Configuration: {'✓ PASS' if config_test else '✗ FAIL'}")
    print(f"Ollama Connection: {'✓ PASS' if connection_test else '✗ FAIL'}")
    print(f"Embedding Model: {'✓ PASS' if model_test else '✗ FAIL'}")
    
    if all([config_test, connection_test, model_test]):
        print("\n🎉 All tests passed! The Ollama memory fix should work.")
    else:
        print("\n⚠ Some tests failed. Please check the issues above.")
    
    print("=" * 60)
